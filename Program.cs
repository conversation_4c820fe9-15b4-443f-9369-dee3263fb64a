// File: Program.cs

using System.Net;
using System.Net.Sockets;
using System.Text;

class Program
{
    static async Task Main(string[] args)
    {
        // 1. Define the IP address and port to listen on.
        // IPAddress.Any would listen on all network interfaces,
        // but for this challenge, we'll stick to localhost.
        IPAddress ipAddress = IPAddress.Loopback;
        int port = 4221;

        // 2. Create a TcpListener instance.
        TcpListener server = new TcpListener(ipAddress, port);

        try
        {
            // 3. Start the listener. This binds the socket to the address
            //    and port and starts listening for incoming connections.
            server.Start();
            Console.WriteLine($"Server started. Listening on {ipAddress}:{port}");

            // 4. Enter an infinite loop to accept client connections.
            //    The server will run until the process is terminated.
            while (true)
            {
                // 5. Asynchronously accept a new TCP client connection.
                //    The 'await' keyword pauses execution here until a client connects.
                TcpClient client = await server.AcceptTcpClientAsync();
                Console.WriteLine("Client connected.");

                // In Stage 2, we handle the connection by sending a response.
                // We wrap the handling in a Task.Run to process it without blocking the loop.
                // Although not strictly necessary for this simple stage, it's good practice
                // for what comes next (concurrency).
                _ = Task.Run(() => HandleClient(client));
            }
        }
        catch (Exception ex)
        {
            Console.WriteLine($"An error occurred: {ex.Message}");
        }
        finally
        {
            // 6. Ensure the server stops listening if the loop exits.
            server.Stop();
        }
    }

    private static async Task HandleClient(TcpClient client)
    {
        try
        {
            // 1. Get the network stream for reading and writing.
            await using NetworkStream stream = client.GetStream();

            // We don't need to read the request for this stage,
            // so we immediately prepare the response.

            // 2. Define the HTTP response.
            //    - "HTTP/1.1 200 OK" is the status line.
            //    - "\r\n" (CRLF) is the standard line terminator in HTTP.
            //    - The final "\r\n" signifies the end of the headers (which are empty here).
            string httpResponse = "HTTP/1.1 200 OK\r\n\r\n";

            // 3. Convert the response string to a byte array. ASCII is sufficient for headers.
            byte[] responseBytes = Encoding.ASCII.GetBytes(httpResponse);

            // 4. Write the response back to the client.
            await stream.WriteAsync(responseBytes, 0, responseBytes.Length);
            Console.WriteLine("Sent '200 OK' response.");
        }
        catch (Exception ex)
        {
            Console.WriteLine($"Error handling client: {ex.Message}");
        }
        finally
        {
            // 5. Close the client connection.
            client.Close();
            Console.WriteLine("Client disconnected.");
        }
    }
}
